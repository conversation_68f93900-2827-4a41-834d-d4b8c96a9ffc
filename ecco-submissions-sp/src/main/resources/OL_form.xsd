<?xml version="1.0" encoding="utf-8"?>
<!-- edited with XMLSpy v2011 rel. 2 sp1 (http://www.altova.com) by <PERSON><PERSON> (University of St Andrews) -->
<!--Created with Liquid XML Studio - FREE Community Edition 7.1.6.1440 (http://www.liquid-technologies.com)-->
<xs:schema xmlns="https://supportingpeople.st-andrews.ac.uk" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="https://supportingpeople.st-andrews.ac.uk" elementFormDefault="qualified" attributeFormDefault="qualified">
    <xs:simpleType name="OwningOrgCode">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0"/>
            <xs:maxInclusive value="999999"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ManGrpSchemes">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0"/>
            <xs:maxInclusive value="999"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ClientRecordID">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="10000"/>
            <xs:maxInclusive value="99999"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="NationalID">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="10000000"/>
            <xs:maxInclusive value="10009999"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="StringData4">
        <xs:restriction base="xs:string">
            <xs:maxLength value="4"/>
            <xs:minLength value="2"/>
            <xs:whiteSpace value="preserve"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="StringData8">
        <xs:restriction base="xs:string">
            <xs:maxLength value="8"/>
            <xs:whiteSpace value="preserve"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="StringData10">
        <xs:restriction base="xs:string">
            <xs:maxLength value="10"/>
            <xs:whiteSpace value="preserve"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="StringData12">
        <xs:restriction base="xs:string">
            <xs:maxLength value="12"/>
            <xs:whiteSpace value="preserve"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="StringData30">
        <xs:restriction base="xs:string">
            <xs:maxLength value="30"/>
            <xs:whiteSpace value="preserve"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="StringData50">
        <xs:restriction base="xs:string">
            <xs:maxLength value="50"/>
            <xs:whiteSpace value="preserve"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="StringData255">
        <xs:restriction base="xs:string">
            <xs:maxLength value="255"/>
            <xs:whiteSpace value="preserve"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ReportingYear0910">
        <xs:restriction base="xs:date">
            <xs:minInclusive value="2009-04-01"/>
            <xs:maxInclusive value="2010-03-31"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ReportingYear1011">
        <xs:restriction base="xs:date">
            <xs:minInclusive value="2010-04-01"/>
            <xs:maxInclusive value="2011-03-31"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ReportingYear1112">
        <xs:restriction base="xs:date">
            <xs:minInclusive value="2011-04-01"/>
            <xs:maxInclusive value="2012-03-31"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="YesNoDK">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="1"/>
            <xs:maxInclusive value="3"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Homeless">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="1"/>
            <xs:enumeration value="2"/>
            <xs:enumeration value="6"/>
            <xs:enumeration value="7"/>
            <xs:enumeration value="8"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="AgeRange">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0"/>
            <xs:maxInclusive value="110"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Sex">
        <xs:restriction base="xs:string">
            <xs:enumeration value="_"/>
            <xs:enumeration value="M"/>
            <xs:enumeration value="F"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Relationship">
        <xs:restriction base="xs:string">
            <xs:enumeration value="_"/>
            <xs:enumeration value="P"/>
            <xs:enumeration value="C"/>
            <xs:enumeration value="X"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="EconomicStatus">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="-1"/>
            <xs:maxInclusive value="9"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ServiceType">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="1"/>
            <xs:enumeration value="6"/>
            <xs:enumeration value="7"/>
            <xs:enumeration value="8"/>
            <xs:enumeration value="9"/>
            <xs:enumeration value="10"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="14"/>
            <xs:enumeration value="15"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="EthnicOrigin">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0"/>
            <xs:maxInclusive value="18"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ClientGroups">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="1"/>
            <xs:enumeration value="2"/>
            <xs:enumeration value="3"/>
            <xs:enumeration value="4"/>
            <xs:enumeration value="5"/>
            <xs:enumeration value="6"/>
            <xs:enumeration value="7"/>
            <xs:enumeration value="8"/>
            <xs:enumeration value="9"/>
            <xs:enumeration value="10"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="15"/>
            <xs:enumeration value="16"/>
            <xs:enumeration value="17"/>
            <xs:enumeration value="18"/>
            <xs:enumeration value="19"/>
            <xs:enumeration value="20"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ReferralSource">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="1"/>
            <xs:maxInclusive value="14"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ReferralType">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="1"/>
            <xs:maxInclusive value="5"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Years">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0"/>
            <xs:maxInclusive value="110"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Months">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0"/>
            <xs:maxInclusive value="11"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Days">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0"/>
            <xs:maxInclusive value="31"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="AccomTypeCRF">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="1"/>
            <xs:enumeration value="2"/>
            <xs:enumeration value="3"/>
            <xs:enumeration value="4"/>
            <xs:enumeration value="6"/>
            <xs:enumeration value="7"/>
            <xs:enumeration value="8"/>
            <xs:enumeration value="9"/>
            <xs:enumeration value="10"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="14"/>
            <xs:enumeration value="15"/>
            <xs:enumeration value="16"/>
            <xs:enumeration value="17"/>
            <xs:enumeration value="18"/>
            <xs:enumeration value="19"/>
            <xs:enumeration value="20"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="26"/>
            <xs:enumeration value="27"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Email">
        <xs:restriction base="xs:string">
            <xs:maxLength value="255"/>
            <xs:pattern value="(_{1}|([0-9a-zA-Z]([-.\w]*[0-9a-zA-Z])*@([0-9a-zA-Z][-\w]*[0-9a-zA-Z]\.)+[a-zA-Z]{2,9}))"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="NINumber">
        <xs:restriction base="xs:string">
            <xs:maxLength value="9"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ONSCode">
        <xs:restriction base="xs:string">
            <xs:pattern value="(0{1}|[0-9]{2}[A-Z]{2})"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="PostcodePart1">
        <xs:restriction base="xs:string">
            <xs:maxLength value="4"/>
            <xs:pattern value="(_{1}|[A-Z]{1,2}[0-9R][0-9A-Z]?)"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="PostcodePart2">
        <xs:restriction base="xs:string">
            <xs:maxLength value="3"/>
            <xs:pattern value="(_{1}|[0-9][A-Z]{2})"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="YesNoMissing">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0"/>
            <xs:maxInclusive value="2"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Religion">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0"/>
            <xs:maxInclusive value="10"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ServiceTypeShort">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="1"/>
            <xs:enumeration value="7"/>
            <xs:enumeration value="8"/>
            <xs:enumeration value="9"/>
            <xs:enumeration value="10"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="14"/>
            <xs:enumeration value="15"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ServiceTypeLong">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="1"/>
            <xs:enumeration value="2"/>
            <xs:enumeration value="3"/>
            <xs:enumeration value="4"/>
            <xs:enumeration value="5"/>
            <xs:enumeration value="7"/>
            <xs:enumeration value="8"/>
            <xs:enumeration value="13"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="AccomType">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0"/>
            <xs:maxInclusive value="30"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_1a">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="34"/>
            <xs:enumeration value="35"/>
            <xs:enumeration value="36"/>
            <xs:enumeration value="40"/>
            <xs:enumeration value="14"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_1b">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="34"/>
            <xs:enumeration value="35"/>
            <xs:enumeration value="40"/>
            <xs:enumeration value="14"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_1c">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="14"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="34"/>
            <xs:enumeration value="35"/>
            <xs:enumeration value="40"/>
            <xs:enumeration value="15"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_2a_i">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="14"/>
            <xs:enumeration value="15"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="34"/>
            <xs:enumeration value="35"/>
            <xs:enumeration value="36"/>
            <xs:enumeration value="40"/>
            <xs:enumeration value="16"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_2a_ii">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="14"/>
            <xs:enumeration value="15"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="34"/>
            <xs:enumeration value="35"/>
            <xs:enumeration value="40"/>
            <xs:enumeration value="16"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_2b">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="14"/>
            <xs:enumeration value="15"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="40"/>
            <xs:enumeration value="16"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_2c">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="14"/>
            <xs:enumeration value="15"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="40"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_2d_i">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="14"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="34"/>
            <xs:enumeration value="35"/>
            <xs:enumeration value="40"/>
            <xs:enumeration value="15"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_2d_ii">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="14"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="40"/>
            <xs:enumeration value="15"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_3_i">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="34"/>
            <xs:enumeration value="35"/>
            <xs:enumeration value="36"/>
            <xs:enumeration value="37"/>
            <xs:enumeration value="40"/>
            <xs:enumeration value="14"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_3_ii">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="34"/>
            <xs:enumeration value="35"/>
            <xs:enumeration value="36"/>
            <xs:enumeration value="37"/>
            <xs:enumeration value="38"/>
            <xs:enumeration value="40"/>
            <xs:enumeration value="14"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_4a">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="34"/>
            <xs:enumeration value="35"/>
            <xs:enumeration value="40"/>
            <xs:enumeration value="14"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_4a_ii">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="34"/>
            <xs:enumeration value="35"/>
            <xs:enumeration value="36"/>
            <xs:enumeration value="37"/>
            <xs:enumeration value="38"/>
            <xs:enumeration value="40"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_4b">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="14"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="34"/>
            <xs:enumeration value="40"/>
            <xs:enumeration value="15"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_4c_i">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="34"/>
            <xs:enumeration value="35"/>
            <xs:enumeration value="36"/>
            <xs:enumeration value="40"/>
            <xs:enumeration value="14"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_4c_ii">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="34"/>
            <xs:enumeration value="35"/>
            <xs:enumeration value="40"/>
            <xs:enumeration value="14"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Reason_5">
        <xs:restriction base="xs:integer">
            <xs:enumeration value="0"/>
            <xs:enumeration value="11"/>
            <xs:enumeration value="12"/>
            <xs:enumeration value="13"/>
            <xs:enumeration value="21"/>
            <xs:enumeration value="22"/>
            <xs:enumeration value="23"/>
            <xs:enumeration value="24"/>
            <xs:enumeration value="25"/>
            <xs:enumeration value="31"/>
            <xs:enumeration value="32"/>
            <xs:enumeration value="33"/>
            <xs:enumeration value="40"/>
            <xs:enumeration value="14"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="YesNoNAMissing">
        <xs:restriction base="xs:integer">
            <xs:minInclusive value="0"/>
            <xs:maxInclusive value="3"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="HHMDetails">
        <xs:annotation>
            <xs:documentation>Houshold Member Details. This group is applied to each Household Member.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="Age" type="AgeRange">
                <xs:annotation>
                    <xs:documentation>Age of the Client or other Household Member.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Sex" type="Sex">
                <xs:annotation>
                    <xs:documentation>Sex of the Client or other Household Member.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Relationship" type="Relationship">
                <xs:annotation>
                    <xs:documentation>Relationship of Household Member to the Client. Enter '_' for the Client.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="EconomicStatus" type="EconomicStatus">
                <xs:annotation>
                    <xs:documentation>Economic Status of Client or other Household Member.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:element name="OL_Forms">
        <xs:annotation>
            <xs:documentation>Collection of OL forms</xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element name="SubmissionDetails">
                    <xs:annotation>
                        <xs:documentation>Information about the person who completed this Client Record Form.</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="Forename" type="StringData50">
                                <xs:annotation>
                                    <xs:documentation>Forename of the person who completed this Client Record Form.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="Surname" type="StringData50">
                                <xs:annotation>
                                    <xs:documentation>Surname of the person who completed this Client Record Form.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="Telephone" type="StringData12">
                                <xs:annotation>
                                    <xs:documentation>Telephone No of the person who completed this Client Record Form.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="Email" type="Email">
                                <xs:annotation>
                                    <xs:documentation>Email address of the person who completed this Client Record Form.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="DateCompleted" type="xs:date">
                                <xs:annotation>
                                    <xs:documentation>Date that this Client Record Form was completed.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="OL_Form" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>Single OL Form</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="ProviderServiceDetails">
                                <xs:annotation>
                                    <xs:documentation>Provider and Service Information</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="NCRP_ID" type="ClientRecordID">
                                            <xs:annotation>
                                                <xs:documentation>National Client Record Provider ID</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="National_ID" type="NationalID">
                                            <xs:annotation>
                                                <xs:documentation>National Provider ID</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="ProviderName" type="StringData255">
                                            <xs:annotation>
                                                <xs:documentation>Provider ID</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="AdminAuth" type="StringData4">
                                            <xs:annotation>
                                                <xs:documentation>SP Administering Authority</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="ServiceName" type="StringData255">
                                            <xs:annotation>
                                                <xs:documentation>Service Name</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="SPServiceID" type="StringData30">
                                            <xs:annotation>
                                                <xs:documentation>SP Service ID</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="SupportPlan" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Agreed Support Plan in Place</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="ClientAgrees" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client Agrees with reported outcomes</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="ServiceType" type="ServiceTypeLong">
                                            <xs:annotation>
                                                <xs:documentation>Service Type</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Partnership" type="YesNoDK">
                                            <xs:annotation>
                                                <xs:documentation>Service worked in partnership with other agencies</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="PartnershipHealth" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Health</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="PartnershipSocial" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Social Services</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="PartnershipHousing" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Housing Services</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="PartnershipDrug" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Drug/Alcohol Services</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="PartnershipPolice" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Police/Probation/Prison</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="PartnershipYOT" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Youth Offending Teams</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="PartnershipEducation" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Education/Training</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="PartnershipBenefits" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Benefits</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="PartnershipDebt" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Debt Rehabilitation Services</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="PartnershipEmployment" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Employment Agencies/Job Centre</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="PartnershipOther" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Other</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="ServiceDuration">
                                <xs:annotation>
                                    <xs:documentation>Service date information</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="Date_Completed" type="ReportingYear1112">
                                            <xs:annotation>
                                                <xs:documentation>Date form was completed</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="ClientDetails">
                                <xs:annotation>
                                    <xs:documentation>Information on the client</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="ClientBudget" type="YesNoDK">
                                            <xs:annotation>
                                                <xs:documentation>Client had an Individual Budget</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="ClientBudgetUse" type="YesNoNAMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client used Individual Budget to purchase service</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="TenantCode" type="StringData12">
                                            <xs:annotation>
                                                <xs:documentation>Unique identifier for client</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="ClientAge" type="AgeRange">
                                            <xs:annotation>
                                                <xs:documentation>Age of client</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="ClientSex" type="Sex">
                                            <xs:annotation>
                                                <xs:documentation>Sex of client</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="ClientEcStat" type="EconomicStatus">
                                            <xs:annotation>
                                                <xs:documentation>Economic status of client</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="EthnicOrigin" type="EthnicOrigin">
                                            <xs:annotation>
                                                <xs:documentation>Ethnic origin of client</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Religion" type="Religion">
                                            <xs:annotation>
                                                <xs:documentation>Religion of client</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="DisabilityYesNo" type="YesNoDK">
                                            <xs:annotation>
                                                <xs:documentation>Does client have a disability?</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Mobility" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Mobility related disability</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Visual" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Visual disability</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Hearing" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Hearing disability</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Chronic" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Progressive illness/Chronic disability</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Mental" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Mental illness</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Learning" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Learning disability</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Autistic" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Autistic disability</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Refused" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Refused to disclose</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Other" type="xs:boolean">
                                            <xs:annotation>
                                                <xs:documentation>Other disability</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="PrimaryClientGroup" type="ClientGroups">
                                            <xs:annotation>
                                                <xs:documentation>Primary Client Group</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="SecondaryClientGroup1" type="ClientGroups">
                                            <xs:annotation>
                                                <xs:documentation>Secondary Client Group</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="SecondaryClientGroup2" type="ClientGroups">
                                            <xs:annotation>
                                                <xs:documentation>Secondary Client Group</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="SecondaryClientGroup3" type="ClientGroups">
                                            <xs:annotation>
                                                <xs:documentation>Secondary Client Group</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="HomeLife" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client intends service to provide home for life</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="MaxIncome_1a">
                                <xs:annotation>
                                    <xs:documentation>Responses for question 1(a)</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="Need" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client had support need to maximise income</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Outcome" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Outcome was successful?</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="OngoingSupport" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client requires ongoing support</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Primary" type="Reason_1a">
                                            <xs:annotation>
                                                <xs:documentation>Primary reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Second" type="Reason_1a">
                                            <xs:annotation>
                                                <xs:documentation>Second reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Third" type="Reason_1a">
                                            <xs:annotation>
                                                <xs:documentation>Third reason for unsuccessful  outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="ManagingDebt_1b">
                                <xs:annotation>
                                    <xs:documentation>Responses for question 1(b)</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="Need" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client had support need for managing debt</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Outcome" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Outcome was successful</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="OngoingSupport" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client requires ongoing support</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Primary" type="Reason_1b">
                                            <xs:annotation>
                                                <xs:documentation>Primary reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Second" type="Reason_1b">
                                            <xs:annotation>
                                                <xs:documentation>Second reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Third" type="Reason_1b">
                                            <xs:annotation>
                                                <xs:documentation>Third reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="PaidWork_1c">
                                <xs:annotation>
                                    <xs:documentation>Responses for question 1(c)</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="Need" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client had support need to find paid work</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Outcome_i" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client found paid work?</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="OngoingSupport_i" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client requires ongoing support</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Primary_i" type="Reason_1c">
                                            <xs:annotation>
                                                <xs:documentation>Primary reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Second_i" type="Reason_1c">
                                            <xs:annotation>
                                                <xs:documentation>Second reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Third_i" type="Reason_1c">
                                            <xs:annotation>
                                                <xs:documentation>Third reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Outcome_ii" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client participated in paid work during the support period</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="OngoingSupport_ii" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Primary reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Primary_ii" type="Reason_1c">
                                            <xs:annotation>
                                                <xs:documentation>Primary reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Second_ii" type="Reason_1c">
                                            <xs:annotation>
                                                <xs:documentation>Second reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Third_ii" type="Reason_1c">
                                            <xs:annotation>
                                                <xs:documentation>Third reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="Training_2a">
                                <xs:annotation>
                                    <xs:documentation>Responses for question 2(a)</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="Need" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client had needs in relation to training/education</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Outcome_i" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client participated in training/education</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="OngoingSupport_i" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client requires ongoing support</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Primary_i" type="Reason_2a_i">
                                            <xs:annotation>
                                                <xs:documentation>Primary reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Second_i" type="Reason_2a_i">
                                            <xs:annotation>
                                                <xs:documentation>Second reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Third_i" type="Reason_2a_i">
                                            <xs:annotation>
                                                <xs:documentation>Third reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Outcome_ii" type="YesNoNAMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client achieved qualifications</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="OngoingSupport_ii" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client requires ongoing support</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Primary_ii" type="Reason_2a_ii">
                                            <xs:annotation>
                                                <xs:documentation>Primary reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Second_ii" type="Reason_2a_ii">
                                            <xs:annotation>
                                                <xs:documentation>Second reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Third_ii" type="Reason_2a_ii">
                                            <xs:annotation>
                                                <xs:documentation>Third reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="Informal_2b">
                                <xs:annotation>
                                    <xs:documentation>Responses for question 2(b)</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="Need" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client had support need for informal learning</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Outcome" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Outcome was successful?</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="OngoingSupport" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client requires ongoing support</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Primary" type="Reason_2b">
                                            <xs:annotation>
                                                <xs:documentation>Primary reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Second" type="Reason_2b">
                                            <xs:annotation>
                                                <xs:documentation>Second reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Third" type="Reason_2b">
                                            <xs:annotation>
                                                <xs:documentation>Third reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="Worklike_2c">
                                <xs:annotation>
                                    <xs:documentation>Responses for question 2(c)</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="Need" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client had support need for worklike activities</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Outcome" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Outcome was successful?</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="OngoingSupport" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client requires ongoing support</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Primary" type="Reason_2c">
                                            <xs:annotation>
                                                <xs:documentation>Primary reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Second" type="Reason_2c">
                                            <xs:annotation>
                                                <xs:documentation>Second reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Third" type="Reason_2c">
                                            <xs:annotation>
                                                <xs:documentation>Third reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="External_2d">
                                <xs:annotation>
                                    <xs:documentation>Responses for question 2(d)</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="Need" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client required to support to find external groups/friends and family</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Outcome_i" type="YesNoNAMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client was successful in finding external groups</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="OngoingSupport_i" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client requires ongoing support</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Primary_i" type="Reason_2d_i">
                                            <xs:annotation>
                                                <xs:documentation>Primary reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Second_i" type="Reason_2d_i">
                                            <xs:annotation>
                                                <xs:documentation>Second reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Third_i" type="Reason_2d_i">
                                            <xs:annotation>
                                                <xs:documentation>Third reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Outcome_ii" type="YesNoNAMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client was successful in finding friends or family</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="OngoingSupport_ii" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client requires ongoing support</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Primary_ii" type="Reason_2d_ii">
                                            <xs:annotation>
                                                <xs:documentation>Primary reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Second_ii" type="Reason_2d_ii">
                                            <xs:annotation>
                                                <xs:documentation>Second reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Third_ii" type="Reason_2d_ii">
                                            <xs:annotation>
                                                <xs:documentation>Third reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="PrimaryCare_3a">
                                <xs:annotation>
                                    <xs:documentation>Responses for question 3(a)</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="Need" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client had support need for physical health care</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Outcome" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Outcome was successful?</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="OngoingSupport" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client requires ongoing support</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Primary" type="Reason_3_i">
                                            <xs:annotation>
                                                <xs:documentation>Primary reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Second" type="Reason_3_i">
                                            <xs:annotation>
                                                <xs:documentation>Second reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Third" type="Reason_3_i">
                                            <xs:annotation>
                                                <xs:documentation>Third reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="MentalHealth_3b">
                                <xs:annotation>
                                    <xs:documentation>Responses for question 3(b)</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="Need" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client had support need in relation to mental health issues</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Outcome" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Outcome was successful?</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="OngoingSupport" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client requires ongoing support</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Primary" type="Reason_3_i">
                                            <xs:annotation>
                                                <xs:documentation>Primary reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Second" type="Reason_3_i">
                                            <xs:annotation>
                                                <xs:documentation>Second reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Third" type="Reason_3_i">
                                            <xs:annotation>
                                                <xs:documentation>Third reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="Substance_3c">
                                <xs:annotation>
                                    <xs:documentation>Responses for question 3(c)</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="Need" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client had support need for substance misuse issues</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Outcome" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Outcome was successful?</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="OngoingSupport" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client requires ongoing support</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Primary" type="Reason_3_ii">
                                            <xs:annotation>
                                                <xs:documentation>Primary reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Second" type="Reason_3_ii">
                                            <xs:annotation>
                                                <xs:documentation>Second reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Third" type="Reason_3_ii">
                                            <xs:annotation>
                                                <xs:documentation>Third reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="Adaptation_3d">
                                <xs:annotation>
                                    <xs:documentation>Responses for question 3(d)</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="Need" type="YesNoNAMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client has support need for adaptation/adaptive technology</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Outcome" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Outcome was successful?</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="OngoingSupport" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client requires ongoing support</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Primary" type="Reason_3_ii">
                                            <xs:annotation>
                                                <xs:documentation>Primary reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Second" type="Reason_3_ii">
                                            <xs:annotation>
                                                <xs:documentation>Second reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Third" type="Reason_3_ii">
                                            <xs:annotation>
                                                <xs:documentation>Third reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="MaintainAccom_4a">
                                <xs:annotation>
                                    <xs:documentation>Responses for question 4a)(i)</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="Need" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client had support need for maintaining accommodation</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Outcome" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Outcome was successful?</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="OngoingSupport" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client requires ongoing support</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Primary" type="Reason_4a">
                                            <xs:annotation>
                                                <xs:documentation>Primary reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Second" type="Reason_4a">
                                            <xs:annotation>
                                                <xs:documentation>Second reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Third" type="Reason_4a">
                                            <xs:annotation>
                                                <xs:documentation>Third reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="SettleAccom_4a">
                                <xs:annotation>
                                    <xs:documentation>Responses for question 4a)(ii)</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="Need" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client had support need to find secure/obtain settled accommodation</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Outcome" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Outcome was successful?</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="OngoingSupport" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client requires ongoing support</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Primary" type="Reason_4a_ii">
                                            <xs:annotation>
                                                <xs:documentation>Primary reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Second" type="Reason_4a_ii">
                                            <xs:annotation>
                                                <xs:documentation>Second reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Third" type="Reason_4a_ii">
                                            <xs:annotation>
                                                <xs:documentation>Third reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="StatOrder_4b">
                                <xs:annotation>
                                    <xs:documentation>Responses for question 4(b)</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="Need" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client had support need in relation to statutory orders</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Outcome" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Outcome was successful?</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="OngoingSupport" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client requires ongoing support</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Primary" type="Reason_4b">
                                            <xs:annotation>
                                                <xs:documentation>Primary reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Second" type="Reason_4b">
                                            <xs:annotation>
                                                <xs:documentation>Second reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Third" type="Reason_4b">
                                            <xs:annotation>
                                                <xs:documentation>Third reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="SelfHarm_4c">
                                <xs:annotation>
                                    <xs:documentation>Responses for question 4c)(i)</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="Need" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client had support need to prevent self-harm</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Outcome" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Outcome was successful?</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="OngoingSupport" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client requires ongoing support</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Primary" type="Reason_4c_i">
                                            <xs:annotation>
                                                <xs:documentation>Primary reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Second" type="Reason_4c_i">
                                            <xs:annotation>
                                                <xs:documentation>Second reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Third" type="Reason_4c_i">
                                            <xs:annotation>
                                                <xs:documentation>Third reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="HarmToOthers_4c">
                                <xs:annotation>
                                    <xs:documentation>Responses for question 4c)(ii)</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="Need" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client had support to prevent harm to others</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Outcome" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Outcome was successful?</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="OngoingSupport" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client requires ongoing support</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Primary" type="Reason_4c_ii">
                                            <xs:annotation>
                                                <xs:documentation>Primary reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Second" type="Reason_4c_ii">
                                            <xs:annotation>
                                                <xs:documentation>Second reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Third" type="Reason_4c_ii">
                                            <xs:annotation>
                                                <xs:documentation>Third reason for unsuccessful  outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="HarmFromOthers_4c">
                                <xs:annotation>
                                    <xs:documentation>Responses for question 4c)(iii)</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="Need" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client had support need to minimise harm from others</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Outcome" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Outcome was successful?</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="OngoingSupport" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client requires ongoing support</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Primary" type="Reason_4c_i">
                                            <xs:annotation>
                                                <xs:documentation>Primary reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Second" type="Reason_4c_i">
                                            <xs:annotation>
                                                <xs:documentation>Second reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Third" type="Reason_4c_i">
                                            <xs:annotation>
                                                <xs:documentation>Third reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="ChoiceControl_5">
                                <xs:annotation>
                                    <xs:documentation>Responses for question 5</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="Need" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client had support need for greater choice/control</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Outcome" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Outcome was successful</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="ContLevel" type="YesNoNAMissing">
                                            <xs:annotation>
                                                <xs:documentation>Contribution Level</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="OngoingSupport" type="YesNoMissing">
                                            <xs:annotation>
                                                <xs:documentation>Client requires ongoing support</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Primary" type="Reason_5">
                                            <xs:annotation>
                                                <xs:documentation>Primary reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Second" type="Reason_5">
                                            <xs:annotation>
                                                <xs:documentation>Second reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="Reason_Third" type="Reason_5">
                                            <xs:annotation>
                                                <xs:documentation>Third reason for unsuccessful outcome</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
