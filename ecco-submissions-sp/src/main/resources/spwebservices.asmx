<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tm="http://microsoft.com/wsdl/mime/textMatching/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:tns="https://supportingpeople.st-andrews.ac.uk/webservices/" xmlns:s="http://www.w3.org/2001/XMLSchema" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" targetNamespace="https://supportingpeople.st-andrews.ac.uk/webservices/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <s:schema elementFormDefault="qualified" targetNamespace="https://supportingpeople.st-andrews.ac.uk/webservices/">
      <s:element name="uploadXML">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="APIKEY" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="XMLData" type="s:string" />
            <s:element minOccurs="0" maxOccurs="1" name="option" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:element name="uploadXMLResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="uploadXMLResult" type="tns:ArrayOfAnyType" />
          </s:sequence>
        </s:complexType>
      </s:element>
      <s:complexType name="ArrayOfAnyType">
        <s:sequence>
          <s:element minOccurs="0" maxOccurs="unbounded" name="anyType" nillable="true" />
        </s:sequence>
      </s:complexType>
      <s:element name="serviceStatus">
        <s:complexType />
      </s:element>
      <s:element name="serviceStatusResponse">
        <s:complexType>
          <s:sequence>
            <s:element minOccurs="0" maxOccurs="1" name="serviceStatusResult" type="s:string" />
          </s:sequence>
        </s:complexType>
      </s:element>
    </s:schema>
  </wsdl:types>
  <wsdl:message name="uploadXMLSoapIn">
    <wsdl:part name="parameters" element="tns:uploadXML" />
  </wsdl:message>
  <wsdl:message name="uploadXMLSoapOut">
    <wsdl:part name="parameters" element="tns:uploadXMLResponse" />
  </wsdl:message>
  <wsdl:message name="serviceStatusSoapIn">
    <wsdl:part name="parameters" element="tns:serviceStatus" />
  </wsdl:message>
  <wsdl:message name="serviceStatusSoapOut">
    <wsdl:part name="parameters" element="tns:serviceStatusResponse" />
  </wsdl:message>
  <wsdl:portType name="ServiceSoap">
    <wsdl:operation name="uploadXML">
      <wsdl:input message="tns:uploadXMLSoapIn" />
      <wsdl:output message="tns:uploadXMLSoapOut" />
    </wsdl:operation>
    <wsdl:operation name="serviceStatus">
      <wsdl:input message="tns:serviceStatusSoapIn" />
      <wsdl:output message="tns:serviceStatusSoapOut" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="ServiceSoap" type="tns:ServiceSoap">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="uploadXML">
      <soap:operation soapAction="https://supportingpeople.st-andrews.ac.uk/webservices/uploadXML" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="serviceStatus">
      <soap:operation soapAction="https://supportingpeople.st-andrews.ac.uk/webservices/serviceStatus" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="ServiceSoap12" type="tns:ServiceSoap">
    <soap12:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="uploadXML">
      <soap12:operation soapAction="https://supportingpeople.st-andrews.ac.uk/webservices/uploadXML" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="serviceStatus">
      <soap12:operation soapAction="https://supportingpeople.st-andrews.ac.uk/webservices/serviceStatus" style="document" />
      <wsdl:input>
        <soap12:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap12:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="Service">
    <wsdl:port name="ServiceSoap" binding="tns:ServiceSoap">
      <soap:address location="https://supportingpeople.st-andrews.ac.uk/webservices/spwebservices.asmx" />
    </wsdl:port>
    <wsdl:port name="ServiceSoap12" binding="tns:ServiceSoap12">
      <soap12:address location="https://supportingpeople.st-andrews.ac.uk/webservices/spwebservices.asmx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>