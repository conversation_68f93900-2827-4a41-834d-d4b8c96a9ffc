package com.ecco.acceptancetests.api.address;

import com.ecco.acceptancetests.api.singleValue.HistoryItemCommandAPITestSupport;
import com.ecco.dto.ChangeViewModel;
import com.ecco.webApi.buildings.FixedContainerViewModel;
import com.ecco.webApi.contacts.address.AddressHistoryViewModel;
import com.ecco.webApi.contacts.address.AddressViewModel;
import com.ecco.webApi.contacts.address.ServiceRecipientAddressLocationChangeCommandViewModel;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


public abstract class BaseAddressHistoryCommandAPITestSupport extends HistoryItemCommandAPITestSupport<AddressHistoryViewModel, ServiceRecipientAddressLocationChangeCommandViewModel> {

    protected List<FixedContainerViewModel> buildings = new ArrayList<>();

    protected void createBuildingAndAddresses(String bldgPart) {
        if (buildings.size() == 0) {
            AddressViewModel avm1 = new AddressViewModel();
            avm1.address = new String[] {"bldg-0", "line2", "line3"};
            avm1.postcode = "BL0 POC";
            var adrId1 = Integer.parseInt(Objects.requireNonNull(addressActor.createAddress(avm1).getBody()).getId());
            buildings.add(buildingActor.createBuilding(unique.nameFor("bldg0 "+bldgPart), adrId1));

            AddressViewModel avm2 = new AddressViewModel();
            avm2.address = new String[] {"bldg-1", "line2", "line3"};
            avm2.postcode = "BL1 POC";
            var adrId2 = Integer.parseInt(Objects.requireNonNull(addressActor.createAddress(avm2).getBody()).getId());
            buildings.add(buildingActor.createBuilding(unique.nameFor("bldg1 "+bldgPart), adrId2));

            AddressViewModel avmUnit = new AddressViewModel();
            avmUnit.address = new String[] {"unit", "line2", "line3"};
            avmUnit.postcode = "UN1 POC";
            var adrIdUnit = Integer.parseInt(Objects.requireNonNull(addressActor.createAddress(avm2).getBody()).getId());
            buildings.add(buildingActor.createUnit(unique.nameFor("bldg1 unit0 "+bldgPart), adrIdUnit, buildings.get(1).buildingId));
        }
    }

    {
        this.testParams = new HistoryItemTestParams<>() {
            int index = 0;
            @Override
            public AddressHistoryViewModel createRandomItem() {
                AddressHistoryViewModel params = new AddressHistoryViewModel();
                params.buildingId = buildings.get(index++).buildingId;
                //params.addressId;
                return params;
            }

            @Override
            public boolean testVars(AddressHistoryViewModel expected, AddressHistoryViewModel actual) {
                return Objects.equals(expected.buildingId, actual.buildingId) &&
                        Objects.equals(expected.addressId, actual.addressId);
            }
        };
    }

    @Override
    protected ServiceRecipientAddressLocationChangeCommandViewModel createCommand(String operation, int serviceRecipientId) {
        return new ServiceRecipientAddressLocationChangeCommandViewModel(operation, serviceRecipientId, null);
    }

    @Override
    protected ServiceRecipientAddressLocationChangeCommandViewModel createCommand(String operation, AddressHistoryViewModel params) {
        ServiceRecipientAddressLocationChangeCommandViewModel vm = new ServiceRecipientAddressLocationChangeCommandViewModel(operation, rvm.serviceRecipientId, rvm.contactId.intValue());
        if (params != null) {
            vm.buildingLocation = ChangeViewModel.create(null, params.buildingId);
            vm.addressLocation = ChangeViewModel.create(null, params.addressId);
        }
        return vm;
    }

    @Override
    protected AddressHistoryViewModel[] getHistory() {
        return addressActor.getAddressHistoryByServiceRecipientIdOrderByValidFromDesc(rvm.serviceRecipientId).getBody();
    }

}