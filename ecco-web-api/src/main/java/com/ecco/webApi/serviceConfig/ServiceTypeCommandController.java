package com.ecco.webApi.serviceConfig;

import javax.annotation.Nonnull;

import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;

import com.ecco.serviceConfig.repositories.ConfigCommandRepository;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.viewModels.Result;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

@RestController
public class ServiceTypeCommandController extends BaseWebApiController {

    @Nonnull private final ConfigCommandRepository configCommandRepository;
    private final TaskDefinitionEntryCommandHandler taskDefinitionEntryCommandHandler;
    private final TaskDefinitionEntrySettingCommandHandler taskDefinitionEntrySettingCommandHandler;
    private final ServiceTypeChangeCommandHandler serviceTypeChangeCommandHandler;

    @Autowired
    public ServiceTypeCommandController(
            @Nonnull ConfigCommandRepository configCommandRepository,
            TaskDefinitionEntryCommandHandler taskDefinitionEntryCommandHandler,
            TaskDefinitionEntrySettingCommandHandler taskDefinitionEntrySettingCommandHandler,
            ServiceTypeChangeCommandHandler serviceTypeChangeCommandHandler) {
        this.configCommandRepository = configCommandRepository;
        this.serviceTypeChangeCommandHandler = serviceTypeChangeCommandHandler;
        this.taskDefinitionEntryCommandHandler = taskDefinitionEntryCommandHandler;
        this.taskDefinitionEntrySettingCommandHandler = taskDefinitionEntrySettingCommandHandler;
    }

    @PostJson("/service-config/servicetype/")
    public Result processServiceTypeChangeCommand(
            @Nonnull Authentication authentication,
            @Nonnull @RequestBody String requestBody) throws JsonParseException, JsonMappingException, IOException {

        return serviceTypeChangeCommandHandler.handleCommand(authentication, null, requestBody);
    }

    @RequestMapping(value = "/service-config/{serviceTypeId}/task/{taskName}/",
            method = RequestMethod.POST, consumes = APPLICATION_JSON_VALUE)
    public Result processTaskDefinitionEntryCommand(
            @Nonnull Authentication authentication,
            TaskDefinitionEntryParams params,
            @Nonnull @RequestBody String requestBody) throws JsonParseException, JsonMappingException, IOException {

        return taskDefinitionEntryCommandHandler.handleCommand(authentication, params, requestBody);
    }

    @RequestMapping(value = "/service-config/{serviceTypeId}/task/{taskName}/setting/{settingName}/",
            method = RequestMethod.POST, consumes = APPLICATION_JSON_VALUE)
    public Result processTaskDefinitionEntrySettingCommand(
            @Nonnull Authentication authentication,
            TaskDefinitionEntrySettingParams params,
            @Nonnull @RequestBody String requestBody) throws JsonParseException, JsonMappingException, IOException {

        return taskDefinitionEntrySettingCommandHandler.handleCommand(authentication, params, requestBody);
    }

}
