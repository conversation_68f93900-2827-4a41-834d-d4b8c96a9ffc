package com.ecco.offline;

import com.ecco.infrastructure.config.ApplicationProperties;

public class AlwaysNetworkResourceProvider extends OfflineResourceProviderBase {

    private final ApplicationProperties applicationProperties;

    public AlwaysNetworkResourceProvider(ApplicationProperties applicationProperties) {

        this.applicationProperties = applicationProperties;

        // NETWORK namespace needs to be conservative as it will prevent other
        // paths getting seen so we need an "always network" one
        addLocalNetworkResource("api/");
        addLocalNetworkResource("dynamic/");

        // Only if no fallback do we do this special-case
        addNetworkResource("*");
    }

    private void addLocalNetworkResource(String path) {
        String applicationRootPath = applicationProperties.getApplicationRootPath();
        addNetworkResource(new NetworkResource(applicationRootPath + path));
    }

    private void addNetworkResource(String path) {
        addNetworkResource(new NetworkResource(path));
    }
}
