import $ = require("jquery");
import {Uuid} from "@eccosolutions/ecco-crypto";

import ActionButton = require("../../controls/ActionButton");
import {CommandQueue} from "ecco-commands";
import {EvidenceDef, EvidenceContext, ActionInstanceControlData, ActionInstanceFeatures} from "ecco-evidence";
import {GoalUpdateCommandDto} from "ecco-dto/evidence-dto";
import {SmartStepStatus} from "ecco-dto";
import EditGoalForm = require("./EditGoalForm");
import RepeatedActionForm = require("./RepeatedActionForm");
import { EccoDateTime } from "@eccosolutions/ecco-common";
import {ActionComponent} from "ecco-dto";
import {SessionData} from "ecco-dto";
import {ActionInstanceControl} from "../../evidence/evidenceControls";
import {StringUtils} from "@eccosolutions/ecco-common";
import {GoalUpdateCommand} from "ecco-commands";
import SupportInstanceControl from "./SupportInstanceControl";

let MAX_TITLE_LEN = 50;

class CheckInstanceControl extends SupportInstanceControl implements ActionInstanceControl {

    // *** STATE
    // sort of state... this only applies to checks, so leaving as-is for now
    private $statusChangeReasonIcon = $("<i>").addClass("hidden");
    private $statusChangeComment = $("<span>").text("");
    // *** STATE

    constructor(context: EvidenceContext, sessionData: SessionData, serviceRecipientId: number,
                evidenceDef: EvidenceDef, actionDef: ActionComponent,
                initialData: ActionInstanceControlData, controlUuid: Uuid,
                controlFeatures: ActionInstanceFeatures) {
        super(context, sessionData, serviceRecipientId, evidenceDef, actionDef, initialData, controlUuid, controlFeatures);
    }

    protected initialise() {
        if (!this.initialActionInstanceUuid()) { throw new Error("instanceUuid cannot be null"); }
        super.initialise();
        this.defaultGoalNameText = "- please add description -";
        this.defaultLastUpdateText = "- not checked yet -";
    }

    protected updateStateFromSnapshot(data: ActionInstanceControlData) {
        super.updateStateFromSnapshot(data);

        this.title = StringUtils.trimText(data.goalName, MAX_TITLE_LEN);

        // command-only currently
        // timestamp of the cmd, but could be work date / created smart step
        this.lastUpdate = data.lastUpdate;

        let statusChangeReasonIconClass = this.sessionData
            .getListDefinitionEntryById(this.latest.statusChangeReasonId).getIconClasses();

        if (statusChangeReasonIconClass) {
            this.$statusChangeReasonIcon
                .removeClass()
                .addClass("fa selected")
                .addClass(statusChangeReasonIconClass);
        } else {
            this.$statusChangeReasonIcon
                .removeClass()
                .addClass("hidden");
        }

        // command-only currently
        // annotationChange['statusChangeComment'] is the last comment on the work
        // Always show the last comment that matched a statusChangeReason change
        if (data.statusChangeComment) {
            this.$statusChangeComment.text(data.statusChangeComment);
        }

    }

    protected updateStateFromCommand(dto: GoalUpdateCommandDto) {
        // these need to be individually updatable.. but for now just bash onto screen
        if (dto.goalNameChange) {
            // need is the comment change
            this.latest.goalName = dto.goalNameChange.to;
            this.title = StringUtils.trimText(dto.goalNameChange.to, MAX_TITLE_LEN);
        }
        if (dto.forceStatusChange && dto.statusChange && dto.statusChange.to == SmartStepStatus.AchievedAndStillRelevant) {
            this.lastUpdate = EccoDateTime.parseIso8601Utc(dto.timestamp);
        }

        // specific state to checks we leave as-was
        if (dto.statusChangeReason) {
            let statusChangeReasonIconClass = this.sessionData
                    .getListDefinitionEntryById(dto.statusChangeReason.to).getIconClasses();

            if (statusChangeReasonIconClass) {
                this.$statusChangeReasonIcon
                        .removeClass()
                        .addClass("fa selected")
                        .addClass(statusChangeReasonIconClass);
            } else {
                this.$statusChangeReasonIcon
                        .removeClass()
                        .addClass("hidden");
            }
        }
        // Always show the last comment that matched a statusChangeReason change
        let statusChangeAnnotation = dto.annotationChange && dto.annotationChange["statusChangeComment"];
        if (dto.statusChangeReason || statusChangeAnnotation) {
            this.$statusChangeComment.text(statusChangeAnnotation && statusChangeAnnotation.to || "");
        }
    }

    protected draw() {
        let editGoalNameBtn = new ActionButton("")
            .iconClasses("fa fa-pencil")
            .addClass("btn btn-xs btn-link")
            .clickSynchronous(() => this.showEditGoalForm())
            .autoDisable(false);

        let recordCheckBtn = new ActionButton(" record check")
            .iconClasses("fa fa-check")
            .addClass("btn btn-default btn-sm pull-right")
            .clickSynchronous( () => this.showRepeatedActionForm() )
            .autoDisable(false);

        let top = $("<span>").addClass("goal-name col-xs-12");
        top.append(this.$goalName);
        top.append(recordCheckBtn.element());
        if (this.adminMode) {
            top.append(editGoalNameBtn.element());
        }

        let lastCheck = $("<span>").addClass("col-xs-12")
                .append(document.createTextNode("last checked: "))
                .append(this.$lastUpdate);

        let statusChange = $("<span>")
                .addClass("col-xs-12")
                .append(this.$statusChangeReasonIcon)
                .append(document.createTextNode(" "))
                .append(this.$statusChangeComment);

        this.getContainer()
            .empty()
            .append(top)
            .append(lastCheck)
            .append(statusChange);
    }

    private showEditGoalForm() {
        let form = new EditGoalForm(this.serviceRecipientId, this.evidenceDef,
                this.getActionDef().getId(), this.initialActionInstanceUuid(), this.latest);
        this.showForm(form);
    }

    private showRepeatedActionForm() {
        let form = new RepeatedActionForm(this.sessionData, this.serviceRecipientId, this.evidenceDef,
                this.latest, this.getActionDef(), this.initialActionInstanceUuid());
        this.showForm(form);
    }

    emitChangesTo(commandQueue: CommandQueue) {
        // we don't emit commands ourselves, the modal's do
    }

    public applyCommands(commandQueue: CommandQueue): Promise<void> {
        return commandQueue.getCommands()
            .then(cmds => {

            if (cmds.length > 2) throw new Error("commandQueue should have no more than 2 entries");
            if (cmds.length == 0) return Promise.resolve();

            // DODGY: a bit dodgy, but we always expect first command to be GoalUpdateCommand
            var loneCommand = cmds[0].toCommandDto(); // we just need to be able to apply after sent

            if (loneCommand.commandName == GoalUpdateCommand.discriminator) {
                return commandQueue.flushCommands() // this also flushes the email command
                    .then( () => this.applyCommand(<GoalUpdateCommandDto>loneCommand) );
            }
            else {
                throw new Error("something went wrong");
            }
        });
    }

    isValid(): boolean {
        return true;
    }

    isRelevant(): boolean {
        return true;
    }

    isAchieved(): boolean {
        return true;
    }

    showSummaryOnly() {
        // do the same as normal - no summary
        this.draw();
    }
}
export = CheckInstanceControl;
