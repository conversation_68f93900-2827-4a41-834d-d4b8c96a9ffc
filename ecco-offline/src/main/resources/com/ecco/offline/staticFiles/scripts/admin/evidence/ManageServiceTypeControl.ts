import BaseControl = require("../../controls/BaseControl");

import OutcomesListControl = require("./OutcomesListControl");
import { showInModalDom } from 'ecco-components';


class ManageServiceTypeControl extends BaseControl {

    public static showInModal(serviceTypeId: number) {
        const form = new ManageServiceTypeControl(serviceTypeId);
        showInModalDom(`Manage service type: ${serviceTypeId}`, form.domElement());
    }


    constructor(serviceTypeId: number) {
        super();
        const control = new OutcomesListControl(serviceTypeId);
        control.load();
        this.element().append( control.element() );
    }

}

export = ManageServiceTypeControl;