import {EccoDateTime} from "@eccosolutions/ecco-common";
import {QuestionnaireWorkDto} from "ecco-dto";
import {SmartStepStatus} from "ecco-dto";
import {SupportWork} from "ecco-dto/evidence-dto";
import Lazy = require("lazy");
import Sequence = LazyJS.Sequence;


/**
 * Importing reports/analysis/types for these classes broke offline
 * TODO - perhaps put in common/types or clean up reports/analysis/types
 * These are currently duplicated!
 */
interface Group<T> {
    key: string;

    /** Items related to this key. This allows them to be passed to a table or chained chart if the representation
     * of this key (e.g. a pie segment or bar in a bar chart) is clicked.
     */
    elements: Sequence<T>;

    count?: number;
}

/**
 * Summary class which handles anything to do with timings and periods based on
 * the results of the Accumulator of hact answers. This class has no reference to what triggers HACT
 * questions - its just manipulation of history and resulting timings
 */
export class NotificationSummaryData {

    public static HACT_PREFER_NOT_TO_ANSWER: string = "0"; // this needs to match the configured answer in the database

    private showDaysInAdvance = 7; // notification shown
    public static daysTriggerToPreLimit = 9 * 31; // (was 14, but need to back-date) from the smart step trigger to the pre question
    private daysPreToPost1Trigger = 90; // 3 months to trigger the second answer (post survey 1)
    public static daysPreToPost1Limit = 240; // 8 months for the second answer (post survey 1)
    private daysPost1ToPost2Trigger = 180; // 6 months for the third answer (after post survey 1)
    public static daysPreToEndHardLimit = (14 + 9) * 31 // was 420, 14 months

    private hactAnswersWrapper: HactAnswerHistorySummaryWrapper; // the answers so far, split into periods

    // qns due, days to due, days to expire
    private notificationPreQns: NotificationPreQnsSummaryData = new NotificationPreQnsSummaryData();
    private notificationPostQns: NotificationPostQnsSummaryData = new NotificationPostQnsSummaryData();

    /**
     * @param hactAnswers - the HACT answers that have been saved
     * @param preSurveyActionHistory - the smart steps that can trigger hact questions
     * @param preSurveyQuestionHistory - the questionnaires that can trigger hact questions
     * @param now - the date to use for calaculating periods (today, but useful for testing)
     * @param showAllOutstanding - show all hact - regardless of the usual relevant time period to show notifications
     */
    constructor(hactAnswers: HactAnswerHistorySummary,
                preSurveyActionHistory: Sequence<IntermediateDataStructureFromActionHistory>,
                preSurveyQuestionHistory: Sequence<IntermediateDataStructureFromQuestionHistory>,
                private now: EccoDateTime, showAllOutstanding = false) {

        this.hactAnswersWrapper = new HactAnswerHistorySummaryWrapper(hactAnswers);
        // calculate things as they stand at 'now'
        this.build(preSurveyActionHistory, preSurveyQuestionHistory, showAllOutstanding);
    }

    public hasPostSurveysDueNow() {
        return this.notificationPostQns.hasPostSurveysDueNow();
    }

    public getQuestionDefIdsOfPostSurveysDue(): number[] {
        return this.notificationPostQns.getQuestionDefIdsOfPostSurveysDue();
    }

    public getQuestionDefIdsOfExpiredPeriod(postSurveyNumber: number): number[] {
        return this.notificationPostQns.getQuestionDefIdsOfExpiredPeriod(postSurveyNumber);
    }

    /**
     * Find the lowest of days to qns due in period 1 or days to qns due in period 2
     * For display purposes.
     */
    public getMinDaysToNextPostSurveyDue(): number {
        return this.notificationPostQns.getMinDaysToNextPostSurveyDue();
    }

    /**
     * Find the lowest of days to qns due to expire in period 1 or days to expire in period 2
     * For display purposes.
     */
    public getMinDaysToNextPostSurveyExpires(): number {
        return this.notificationPostQns.getMinDaysToNextPostSurveyExpires();
    }

    /**
     * Min days of the actionDefIds passed - which allows us to pass the real
     * list of allowedActionDefIds which can produce pre-questions
     */
    public getMinDaysToTriggeredActionsExpires(allowedTriggeredActionDefIds: number[]): number {
        return this.notificationPreQns.getMinDaysToTriggeredActionsExpires(allowedTriggeredActionDefIds, this.now);
    }

    public getMinDaysToTriggeredQuestionsExpires(allowedTriggeredQuestionDefIds: number[]): number {
        return this.notificationPreQns.getMinDaysToTriggeredQuestionsExpires(allowedTriggeredQuestionDefIds, this.now);
    }

    public getTriggeredActionDefIdsRecent(): number[] {
        return this.notificationPreQns.getTriggeredActionDefIdsRecent();
    }

    public getTriggeredActionDefIdsNotRecent(): number[] {
        return this.notificationPreQns.getTriggeredActionDefIdsNotRecent();
    }

    public getTriggeredQuestionDefIdsRecent(): number[] {
        return this.notificationPreQns.getTriggeredQuestionDefIdsRecent();
    }

    public getTriggeredQuestionDefIdsNotRecent(): number[] {
        return this.notificationPreQns.getTriggeredQuestionDefIdsNotRecent();
    }

    public getEarliestDateTriggeredAction(actionDefIds: number[]): EccoDateTime {
        return this.notificationPreQns.getEarliestDateTriggeredAction(actionDefIds);
    }

    public getEarliestDateTriggeredActionNotRecent(actionDefIds: number[]): EccoDateTime {
        return this.notificationPreQns.getEarliestDateTriggeredActionNotRecent(actionDefIds);
    }

    public getEarliestDateTriggeredActionRecent(actionDefIds: number[]): EccoDateTime {
        return this.notificationPreQns.getEarliestDateTriggeredActionRecent(actionDefIds);
    }

    public getQuestionDefIdsOfPreSurveyAnswers(): number[] {
        return this.hactAnswersWrapper.getQuestionDefIdsOfPreSurveyAnswers();
    }

    public getSurveyAnsweredQuestionDefIds() {
        return this.hactAnswersWrapper.getSurveyAnsweredQuestionDefIds();
    }

    public getSurveyAnsweredPreSurveyWorkDateForQuestionDefId(qnDefId: number): EccoDateTime {
        return this.hactAnswersWrapper.getSurveyAnsweredPreSurveyWorkDateForQuestionDefId(qnDefId);
    }

    public getSurveyAnsweredPreSurveyAnswerValueForQuestionDefId(qnDefId: number): string {
        return this.hactAnswersWrapper.getSurveyAnsweredPreSurveyAnswerValueForQuestionDefId(qnDefId);
    }

    public getSurveyAnsweredPostSurveyWorkDateForQuestionDefId(postSurveyNumber: number, qnDefId: number): EccoDateTime {
        return this.hactAnswersWrapper.getSurveyAnsweredPostSurveyWorkDateForQuestionDefId(postSurveyNumber, qnDefId)
    }

    public getSurveyAnsweredPostSurvey1AnswerValueForQuestionDefId(qnDefId: number): string {
        return this.hactAnswersWrapper.getSurveyAnsweredPostSurveyAnswerValueForQuestionDefId(1, qnDefId);
    }

    public getSurveyAnsweredPostSurvey2AnswerValueForQuestionDefId(qnDefId: number): string {
        return this.hactAnswersWrapper.getSurveyAnsweredPostSurveyAnswerValueForQuestionDefId(2, qnDefId);
    }


    // ******************************
    // DATA STRUCTURE BUILDERS

    /**
     * Apply timings for
     */
    private build(preSurveyActionData: Sequence<IntermediateDataStructureFromActionHistory>,
                  preSurveyQuestionData: Sequence<IntermediateDataStructureFromQuestionHistory>,
                  showAllOutstanding: boolean): void {

        // note the recent and expired actions
        this.notificationPreQns.build(this.now, preSurveyActionData, preSurveyQuestionData);

        this.hactAnswersWrapper.build(this.now, this.daysPreToPost1Trigger, this.daysPost1ToPost2Trigger);

        // buildDueAndExpiredPreSurveys - calculates the pre survey data based on support history
        this.notificationPreQns.buildDueAndExpiredPreSurveys();

        // buildDueAndExpiredPostSurveys - calculate the post survey data based on question answer history
        this.notificationPostQns.buildDuePostSurvey(this.hactAnswersWrapper, this.showDaysInAdvance, showAllOutstanding);

        this.notificationPostQns.buildExpiredPostSurveys(this.hactAnswersWrapper, this.showDaysInAdvance, showAllOutstanding);

        this.notificationPostQns.buildDueAndExpireDays(this.now, this.daysPreToPost1Trigger, this.daysPost1ToPost2Trigger, this.hactAnswersWrapper.getAnswersPreSurvey());
    }

}


// *********************
// ANSWERS ANALYSIS

/**
 * Accumulator for the summary of all the hact questionnaire answers/work
 * where each answer is placed in order of pre/post1/post2
 * answersForNext puts the last answer as being ready to ask again
 * This is just a bucket-sorter, it does nothing about timings
 */
export class HactAnalysisFromHactAnswerHistoryAccumulator {

    private hactAnswersMemo: HactAnswerHistorySummary;

    constructor(key: string) {
        this.hactAnswersMemo = {
            key: key,
            answersAll: [],
            answersPreSurvey: [], // the first answer
            answersPostSurveys: Lazy([]), // successive answers
            answersForNextPostSurveys: Lazy([]) // questions due in next time slot (with previous survey answer)
            //answersForNext12Months: [] // show the last answers (if continuity is broken, the answers aren't available)
        };
    }

    /** entry method */
    public reduce(hactAnswerWorkIn: Sequence<QuestionnaireWorkDto>): HactAnswerHistorySummary {

        var result = hactAnswerWorkIn

            // NB work is ORDERED by work desc, created desc
            // we need to REVERSE so its in the date it was done
            .reverse()

            .reduce((prev, hactAnswerWork) =>
                this.accumulate(prev, hactAnswerWork), this.hactAnswersMemo);

        this.postProcess();

        return result;
    }

    private accumulate(summary: HactAnswerHistorySummary, hactAnswerWork: QuestionnaireWorkDto): HactAnswerHistorySummary {
        return accumulateHactAnswerHistoryToSummary(
            summary,
            buildHactAnswerDataStructure(hactAnswerWork));
    }

    private postProcess(): void {
        this.populateAnswersForNextPostSurveyFromPreviousData(1, this.hactAnswersMemo.answersPreSurvey);
        this.hactAnswersMemo.answersPostSurveys.each((grp) => {
            let index = Number(grp.key) + 1; // populate the next post survey from this survey elements
            let elements = grp.elements.toArray();
            this.populateAnswersForNextPostSurveyFromPreviousData(index, elements);
        });
    }

    private populateAnswersForNextPostSurveyFromPreviousData(index: number, previousData: IntermediateDataStructureFromHactAnswerWork[]): void {

        // where the answer to the pre survey was not "prefer not to answer"
        let elements = previousData
            .filter((answer) => {
                return answer.preferNotToAnswer == false;
            })
            .filter((answer) => {
                let qns = this.hactAnswersMemo.answersPostSurveys
                    .filter((grp) => grp.key == index.toString())
                    .map((grp) => grp.elements
                        .map((existingAnswer) => existingAnswer.questionDefId))
                    .flatten<number>()
                    .toArray();
                let foundSurvey = qns.indexOf(answer.questionDefId) > -1;
                return !foundSurvey;
            })
            .map((answer) => {
                let nextAnswer: IntermediateHactAnswerDueNextDataStructure = {
                    previousPeriodAnswer: answer,
                    daysToStartOfPeriod: undefined,
                    daysToEndOfPeriod: undefined,
                    daysToEndOfLastPeriod: undefined
                };
                return nextAnswer;
            });
        if (elements.length > 0) {
            let grp: Group<IntermediateHactAnswerDueNextDataStructure> = {
                key: index.toString(),
                elements: Lazy(elements)
            };
            this.hactAnswersMemo.answersForNextPostSurveys = this.hactAnswersMemo.answersForNextPostSurveys.concat(grp);
        }
    }

}

export interface HactAnswerHistorySummary {
    key: string;
    answersAll: IntermediateDataStructureFromHactAnswerWork[]; // all hact answers
    answersPreSurvey: IntermediateDataStructureFromHactAnswerWork[]; // the first hact answer
    answersPostSurveys: Sequence<Group<IntermediateDataStructureFromHactAnswerWork>>; // successive hact answers
    answersForNextPostSurveys: Sequence<Group<IntermediateHactAnswerDueNextDataStructure>>; // questions due in next time slot (with previous survey answer)
}

/**
 * Utility class for the answers so far.
 * Holds the answers so far, split into periods
 * Takes the results of the accumulator (where all the hact answers
 * are placed in order of pre/post1/post2, and answersForNext puts the last answer as being ready to ask again
 */
class HactAnswerHistorySummaryWrapper {
    constructor(private summary: HactAnswerHistorySummary) {
    }

    public getAnswersForNextPostSurvey(survey: number): IntermediateHactAnswerDueNextDataStructure[] {
        return this.summary.answersForNextPostSurveys
            .filter((grp) => grp.key == survey.toString())
            .map((grp) => grp.elements)
            .flatten<IntermediateHactAnswerDueNextDataStructure>()
            .toArray();
    }

    public getAnswersPreSurvey() {
        return this.summary.answersPreSurvey;
    }

    public getQuestionDefIdsOfPreSurveyAnswers(): number[] {
        return Lazy(this.summary.answersPreSurvey)
            .map((data) => data.questionDefId)
            .flatten<number>().toArray();
    }

    public getSurveyAnsweredQuestionDefIds() {
        let postSurveyQns: number[] = this.summary.answersPostSurveys
            .map((survey) => survey.elements
                .map((ans) => ans.questionDefId))
            .flatten<number>()
            .toArray();
        let allQns = Lazy(this.summary.answersPreSurvey).map((ans) => ans.questionDefId)
            .concat(postSurveyQns)
            .uniq().toArray();
        return allQns;
    }

    public getSurveyAnsweredPreSurveyWorkDateForQuestionDefId(qnDefId: number): EccoDateTime {
        let preSurveyAnswered: IntermediateDataStructureFromHactAnswerWork[] = this.summary.answersPreSurvey
            .filter((pre) => pre.questionDefId == qnDefId);
        return preSurveyAnswered.length > 0 ? preSurveyAnswered[0].workDate : null;
    }

    public getSurveyAnsweredPreSurveyAnswerValueForQuestionDefId(qnDefId: number): string {
        let preSurveyAnswered: IntermediateDataStructureFromHactAnswerWork[] = this.summary.answersPreSurvey
            .filter((pre) => pre.questionDefId == qnDefId);
        return preSurveyAnswered.length > 0 ? preSurveyAnswered[0].answerValue : null;
    }

    public getSurveyAnsweredPostSurveyWorkDateForQuestionDefId(postSurveyNumber: number, qnDefId: number): EccoDateTime {
        const postSurveyAnswered = this.getSurveyAnsweredPostSurveyForQuestionDefId(postSurveyNumber, qnDefId);
        return postSurveyAnswered ? postSurveyAnswered.workDate : null;
    }

    public getSurveyAnsweredPostSurveyAnswerValueForQuestionDefId(postSurveyNumber: number, qnDefId: number): string {
        const postSurveyAnswered = this.getSurveyAnsweredPostSurveyForQuestionDefId(postSurveyNumber, qnDefId);
        return postSurveyAnswered ? postSurveyAnswered.answerValue : null;
    }

    private getSurveyAnsweredPostSurveyForQuestionDefId(postSurveyNumber: number, qnDefId: number): IntermediateDataStructureFromHactAnswerWork | null {
        const int = this.summary.answersPostSurveys
            .filter((grp) => grp.key == postSurveyNumber.toString())
            .map((survey) => survey.elements
                .filter((pre) => pre.questionDefId == qnDefId))
            .flatten<IntermediateDataStructureFromHactAnswerWork>()
            .toArray();
        return int.length > 0 ? int[0] : null;
    }

    public build(now: EccoDateTime, daysPreToPost1: number, daysPost1ToPost2: number) {

        this.getAnswersForNextPostSurvey(1)
            .forEach((forPostSurvey1) => {
                // now minus last week = eg, 7 days
                let daysDiff = now.subtractDateTime(forPostSurvey1.previousPeriodAnswer.workDate, true).getDays();
                // daysToStartOfPeriod = 7 minus daysPreToPost1Trigger (90) = -83 days to start of post1
                forPostSurvey1.daysToStartOfPeriod = daysDiff - daysPreToPost1;
                forPostSurvey1.daysToEndOfPeriod = NotificationSummaryData.daysPreToPost1Limit - daysDiff;
                forPostSurvey1.daysToEndOfLastPeriod = NotificationSummaryData.daysPreToEndHardLimit - daysDiff;
            });

        this.getAnswersForNextPostSurvey(2)
            .forEach((forPostSurvey2) => {
                let daysDiff = now.subtractDateTime(forPostSurvey2.previousPeriodAnswer.workDate, true).getDays();
                forPostSurvey2.daysToStartOfPeriod = daysDiff - daysPost1ToPost2;

                let preSurvey = this.getAnswersPreSurvey().filter((answerPre) =>
                    answerPre.questionDefId == forPostSurvey2.previousPeriodAnswer.questionDefId)[0];
                let daysDiffPreSurvey = now.subtractDateTime(preSurvey.workDate, true).getDays();
                forPostSurvey2.daysToEndOfPeriod = NotificationSummaryData.daysPreToEndHardLimit - daysDiffPreSurvey;
                forPostSurvey2.daysToEndOfLastPeriod = forPostSurvey2.daysToEndOfPeriod;
            });
    }

    public appropriateAnswersForPeriod1(showDaysInAdvance: number, showAllOutstanding = false): IntermediateHactAnswerDueNextDataStructure[] {
        // answersForNextPostSurvey1 has the pre survey data ready to analyse
        let postQnsDuePeriod1 = this.getAnswersForNextPostSurvey(1)
            .filter((forPostSurvey1) => {
                // if daysToStartOfPeriod > 0 then the start of period has started
                let isNowDue = forPostSurvey1.daysToStartOfPeriod + showDaysInAdvance >= 0;
                let isInFirstPeriod = forPostSurvey1.daysToEndOfPeriod >= 0;
                let isExpired = forPostSurvey1.daysToEndOfLastPeriod <= 0;
                return (isNowDue || showAllOutstanding) && isInFirstPeriod && !isExpired;
            });
        return postQnsDuePeriod1;
    }

    public appropriateAnswersForOutsidePeriod1(expired: boolean): IntermediateHactAnswerDueNextDataStructure[] {
        // answersForNextPostSurvey1 has the pre survey data ready to analyse
        let outsidePeriod1 = this.getAnswersForNextPostSurvey(1)
            .filter((forPostSurvey1) => {
                let isOutsideFirstPeriod = forPostSurvey1.daysToEndOfPeriod < 0;
                let isExpired = forPostSurvey1.daysToEndOfLastPeriod <= 0;
                return isOutsideFirstPeriod && (expired ? isExpired : !isExpired);
            });
        return outsidePeriod1;
    }


    public appropriateAnswersForPeriod2(expired: boolean, showDaysInAdvance: number, showAllOutstanding = false): IntermediateHactAnswerDueNextDataStructure[] {
        // answersForNextPostSurvey2 has the post survey 1 data ready to analyse
        let appropriateAnswersForPeriod2 = this.getAnswersForNextPostSurvey(2)
            .filter((forPostSurvey2) => {
                let isNowDue = forPostSurvey2.daysToStartOfPeriod + showDaysInAdvance >= 0;
                let isExpiredSincePreSurvey = forPostSurvey2.daysToEndOfPeriod <= 0;
                return (isNowDue || showAllOutstanding) && (expired ? isExpiredSincePreSurvey : !isExpiredSincePreSurvey);
            });
        return appropriateAnswersForPeriod2;
    }

}

/**
 * Build up a summary from each work item.
 * Each work item is ordered by oldest first (since we reversed it)
 * Therefore the accumulator only concerns itself with the first year's data
 */
function accumulateHactAnswerHistoryToSummary(summary: HactAnswerHistorySummary,
                                              answers: IntermediateDataStructureFromHactAnswerWork[]): HactAnswerHistorySummary {

    answers.forEach((answer) => {

        let totalAnswersForQnSoFar = summary.answersAll.filter((item) =>
            answer.questionDefId == item.questionDefId
        ).length;

        if (totalAnswersForQnSoFar == 0) {
            summary.answersPreSurvey.push(answer);
        } else {
            let groupsMatching = summary.answersPostSurveys.filter((grp) => grp.key == totalAnswersForQnSoFar.toString());
            if (groupsMatching.size() > 0) {
                let grp = groupsMatching.first();
                grp.elements = grp.elements.concat(answer);
            } else {
                let grp: Group<IntermediateDataStructureFromHactAnswerWork> = {
                    key: totalAnswersForQnSoFar.toString(),
                    elements: Lazy([answer])
                };
                summary.answersPostSurveys = summary.answersPostSurveys.concat(grp);
            }
        }

        summary.answersAll.push(answer);
    });

    return summary;
}

/**
 * Specify what we need to calculate various things from the underlying questionnaire work
 */
export interface IntermediateHactAnswerDueNextDataStructure {
    // days... -ve are still useful and show the amount behind
    daysToStartOfPeriod: number; //
    daysToEndOfPeriod: number; // eg period1, period2
    daysToEndOfLastPeriod: number; // hard limit
    previousPeriodAnswer: IntermediateDataStructureFromHactAnswerWork;
}

/**
 * Specify what we need from a questionnaire work item
 */
export interface IntermediateDataStructureFromHactAnswerWork {
    questionDefId: number;
    workDate: EccoDateTime;
    preferNotToAnswer: boolean;
    answerValue: string;
}

/**
 * For each work item's answers, build a form of: {questionDefId : workDate : preferNotToAnswer : answer}
 */
function buildHactAnswerDataStructure(hactAnswerWorkIn: QuestionnaireWorkDto): IntermediateDataStructureFromHactAnswerWork[] {
    let result: IntermediateDataStructureFromHactAnswerWork[] = [];
    if (hactAnswerWorkIn.answers != null) {
        hactAnswerWorkIn.answers.forEach((answer) => {
            let workDateTime = EccoDateTime.parseIso8601(hactAnswerWorkIn.workDate);
            let data: IntermediateDataStructureFromHactAnswerWork = {
                questionDefId: answer.questionId,
                workDate: workDateTime,
                preferNotToAnswer: answer.answer == NotificationSummaryData.HACT_PREFER_NOT_TO_ANSWER,
                answerValue: answer.answer
            };
            result.push(data);
        });
    }
    return result;
}


// *********************
// SUPPORT ANALYSIS

/**
 * Takes the action history and extracts WantToAchieve and extracts properties needed
 */
export function convertActionHistoryToIntermediateData(input: Sequence<SupportWork>): Sequence<IntermediateDataStructureFromActionHistory> {

    return input
        .filter((work) => work.actions != null)
        .map((work) => work.actions
            .map((action) => {
                let workDateTime = EccoDateTime.parseIso8601(work.workDate);
                let data = {
                    statusChange: action.statusChange,
                    status: action.status,
                    actionDefId: action.actionId,
                    workDate: workDateTime
                };
                return data;
            })
            .filter((data) => {
                return data.statusChange && data.status == SmartStepStatus.WantToAchieve;
            })
            .map((data) => {
                let struct: IntermediateDataStructureFromActionHistory = {
                    triggeredActionDefId: data.actionDefId,
                    workDate: data.workDate,
                    daysUntilExpire: undefined
                };
                return struct;
            })
        )
        .flatten<IntermediateDataStructureFromActionHistory>();
}

export interface IntermediateDataStructureFromActionHistory {
    triggeredActionDefId: number;
    workDate: EccoDateTime;
    // days... -ve are still useful and show the amount behind
    daysUntilExpire: number; // days util the questions expire
}

/**
 * Takes the question history and extracts properties needed
 */
export function convertQuestionnaireHistoryToIntermediateData(input: Sequence<QuestionnaireWorkDto>): Sequence<IntermediateDataStructureFromQuestionHistory> {

    return input
        .filter((work) => work.answers != null)
        .map((work) => work.answers
            .map((answer) => {
                let workDateTime = EccoDateTime.parseIso8601(work.workDate);
                let data = {
                    answer: answer.answer,
                    questionDefId: answer.questionId,
                    workDate: workDateTime,
                    daysUntilExpire: undefined
                };
                return data;
            })
            .map((data) => {
                let struct: IntermediateDataStructureFromQuestionHistory = {
                    triggeredQuestionDefId: data.questionDefId,
                    workDate: data.workDate,
                    daysUntilExpire: undefined
                };
                return struct;
            })
        )
        .flatten<IntermediateDataStructureFromQuestionHistory>();
}

export interface IntermediateDataStructureFromQuestionHistory {
    triggeredQuestionDefId: number;
    workDate: EccoDateTime;
    // days... -ve are still useful and show the amount behind
    daysUntilExpire: number; // days util the questions expire
}


// *********************
// PRE QNS SUMMARY

/**
 * Pre question encapsulation
 */
class NotificationPreQnsSummaryData {
    constructor() {
    }

    private _triggeredHistoryActionsRecent: IntermediateDataStructureFromActionHistory[];
    private _triggeredHistoryActionsNotRecent: IntermediateDataStructureFromActionHistory[]; // qns expired
    private _triggeredHistoryQuestionsRecent: IntermediateDataStructureFromQuestionHistory[];
    private _triggeredHistoryQuestionsNotRecent: IntermediateDataStructureFromQuestionHistory[]; // qns expired

    public getMinDaysToTriggeredActionsExpires(allowedTriggeredActionDefIds: number[], now: EccoDateTime): number {
        let allowedStruct = this._triggeredHistoryActionsRecent
            .filter((int) => {
                return allowedTriggeredActionDefIds.indexOf(int.triggeredActionDefId) > -1;
            });

        return this.getMinDaysToTriggeredExpires(now, allowedStruct);
    }

    public getMinDaysToTriggeredQuestionsExpires(allowedTriggeredQuestionDefIds: number[], now: EccoDateTime): number {

        let allowedStruct = this._triggeredHistoryQuestionsRecent
            .filter((int) => {
                return allowedTriggeredQuestionDefIds.indexOf(int.triggeredQuestionDefId) > -1;
            });
        return this.getMinDaysToTriggeredExpires(now, allowedStruct);
    }

    private getMinDaysToTriggeredExpires(now: EccoDateTime, allowedStruct: { workDate: EccoDateTime }[]): number {
        let minValue = null;
        if (allowedStruct.length > 0) {
            minValue = allowedStruct.reduce((prev, curr) => {
                return prev.workDate.earlierThan(curr.workDate) ? prev : curr;
            });
        }

        return minValue == null ? null :
            NotificationSummaryData.daysTriggerToPreLimit - now.subtractDateTime(minValue.workDate, true).getDays();
    }

    public getTriggeredActionDefIdsRecent(): number[] {
        return this._triggeredHistoryActionsRecent.map(int => int.triggeredActionDefId);
    }

    public getTriggeredActionDefIdsNotRecent(): number[] {
        return this._triggeredHistoryActionsNotRecent.map(int => int.triggeredActionDefId);
    }

    public getTriggeredQuestionDefIdsRecent(): number[] {
        return this._triggeredHistoryQuestionsRecent.map(int => int.triggeredQuestionDefId);
    }

    public getTriggeredQuestionDefIdsNotRecent(): number[] {
        return this._triggeredHistoryQuestionsNotRecent.map(int => int.triggeredQuestionDefId);
    }

    public getEarliestDateTriggeredAction(actionDefIds: number[]): EccoDateTime {
        return Lazy(this._triggeredHistoryActionsRecent.concat(this._triggeredHistoryActionsNotRecent))
            .filter((action) => actionDefIds.indexOf(action.triggeredActionDefId) > -1)
            .map((action) => action.workDate)
            .reduce((prev, curr) => {
                return prev.earlierThan(curr) ? prev : curr;
            });
    }

    public getEarliestDateTriggeredActionNotRecent(actionDefIds: number[]): EccoDateTime {
        return Lazy(this._triggeredHistoryActionsNotRecent)
            .filter((action) => actionDefIds.indexOf(action.triggeredActionDefId) > -1)
            .map((action) => action.workDate)
            .reduce((prev, curr) => {
                return prev.earlierThan(curr) ? prev : curr;
            });
    }

    public getEarliestDateTriggeredActionRecent(actionDefIds: number[]): EccoDateTime {
        return Lazy(this._triggeredHistoryActionsRecent)
            .filter((action) => actionDefIds.indexOf(action.triggeredActionDefId) > -1)
            .map((action) => action.workDate)
            .reduce((prev, curr) => {
                return prev.earlierThan(curr) ? prev : curr;
            });
    }

    /**
     * Calculates only the daysUntilExpire - since pre surveys are always due
     */
    public buildDueAndExpiredPreSurveys() {

        // note the recent actions
        this._triggeredHistoryActionsRecent = this._triggeredHistoryActionsRecent
            .filter((triggeredAction) => {
                return triggeredAction.daysUntilExpire >= 0;
            });

        // note the expired actions
        this._triggeredHistoryActionsNotRecent = this._triggeredHistoryActionsNotRecent
            .filter((triggeredAction) => {
                return triggeredAction.daysUntilExpire < 0;
            });

        // note the recent actions
        this._triggeredHistoryQuestionsRecent = this._triggeredHistoryQuestionsRecent
            .filter((triggeredQuestion) => {
                return triggeredQuestion.daysUntilExpire >= 0;
            });

        // note the expired actions
        this._triggeredHistoryQuestionsNotRecent = this._triggeredHistoryQuestionsNotRecent
            .filter((triggeredQuestion) => {
                return triggeredQuestion.daysUntilExpire < 0;
            });
    }

    public build(now: EccoDateTime,
                 preSurveyActionData: Sequence<IntermediateDataStructureFromActionHistory>,
                 preSurveyQuestionData: Sequence<IntermediateDataStructureFromQuestionHistory>) {

        const populateDaysUntilExpire = (data: { workDate: EccoDateTime, daysUntilExpire: number }) => {
            let daysDiff = now.subtractDateTime(data.workDate, true).getDays();
            data.daysUntilExpire = NotificationSummaryData.daysTriggerToPreLimit - daysDiff;
            return true;
        };

        // construct new arrays whilst populating the data
        this._triggeredHistoryActionsRecent = preSurveyActionData.filter(data => populateDaysUntilExpire(data)).toArray();
        this._triggeredHistoryActionsNotRecent = preSurveyActionData.filter(data => populateDaysUntilExpire(data)).toArray();
        this._triggeredHistoryQuestionsRecent = preSurveyQuestionData.filter(data => populateDaysUntilExpire(data)).toArray();
        this._triggeredHistoryQuestionsNotRecent = preSurveyQuestionData.filter(data => populateDaysUntilExpire(data)).toArray();
    }

}

// *********************
// POST QNS SUMMARY

/**
 * Post question encapsulation
 */
class NotificationPostQnsSummaryData {
    constructor() {
    }

    private _postQnsDuePeriod1: IntermediateHactAnswerDueNextDataStructure[];
    private _postQnsDuePeriod2: IntermediateHactAnswerDueNextDataStructure[];
    private _postQnsDaysDuePeriod1: number; // days due to the earliest post qn in period 1
    private _postQnsDaysDuePeriod2: number; // days due to the earliest post qn in period 2
    private _postQnsDaysExpirePeriod1: number; // days due to the earliest expired qn in period 1
    private _postQnsDaysExpirePeriod2: number; // days due to the earliest expired qn in period 1
    // period1 is a soft-expiry, since it just rolls onto period2
    // but this is still useful to know in reporting
    private _postQnsExpiredPeriod1: IntermediateHactAnswerDueNextDataStructure[];
    private _postQnsExpiredPeriod2: IntermediateHactAnswerDueNextDataStructure[];

    public getMinDaysToNextPostSurveyDue(): number {
        if (isFinite(this._postQnsDaysDuePeriod1)
            && isFinite(this._postQnsDaysDuePeriod2)) {
            return Math.min(this._postQnsDaysDuePeriod1,
                this._postQnsDaysDuePeriod2);
        } else {
            if (isFinite(this._postQnsDaysDuePeriod1)) {
                return this._postQnsDaysDuePeriod1;
            }
        }
        return this._postQnsDaysDuePeriod2;
    }

    public getMinDaysToNextPostSurveyExpires(): number {
        if (isFinite(this._postQnsDaysExpirePeriod1)
            && isFinite(this._postQnsDaysExpirePeriod2)) {
            return Math.min(this._postQnsDaysExpirePeriod1,
                this._postQnsDaysExpirePeriod2);
        } else {
            if (isFinite(this._postQnsDaysExpirePeriod1)) {
                return this._postQnsDaysExpirePeriod1;
            }
        }
        return this._postQnsDaysExpirePeriod2;
    }

    public hasPostSurveysDueNow() {
        let hasPost1 = this._postQnsDuePeriod1.length > 0;
        let hasPost2 = this._postQnsDuePeriod2.length > 0;
        return hasPost1 || hasPost2;
    }

    public getQuestionDefIdsOfPostSurveysDue(): number[] {
        return Lazy(this._postQnsDuePeriod1.concat(this._postQnsDuePeriod2))
            .map((data) => data.previousPeriodAnswer.questionDefId)
            .flatten<number>().toArray();
    }

    public getQuestionDefIdsOfExpiredPeriod(postSurveyNumber: number): number[] {
        let postExpiredPeriod = postSurveyNumber == 1
            ? this._postQnsExpiredPeriod1
            : this._postQnsExpiredPeriod2;

        return Lazy(postExpiredPeriod)
            .map((data) => data.previousPeriodAnswer.questionDefId)
            .flatten<number>().toArray();
    }

    public buildDuePostSurvey(answerMemo: HactAnswerHistorySummaryWrapper, showDaysInAdvance: number, showAllOutstanding = false) {

        // PERIOD 1
        // check those due for second answer (post survey 1) are in range by using the presurvey
        this._postQnsDuePeriod1 = answerMemo.appropriateAnswersForPeriod1(showDaysInAdvance, showAllOutstanding);

        // SURVEY 1 shows in PERIOD 2
        // due in period 1 but this slips to period 2
        // in terms of notifications and expiry dates
        let dueOutsidePeriod1 = answerMemo.appropriateAnswersForOutsidePeriod1(false);

        // PERIOD 2
        // check those due for third answer (post survey 2) are in range by using post survey 1
        let postQnsDuePeriod2 = answerMemo.appropriateAnswersForPeriod2(false, showDaysInAdvance, showAllOutstanding);
        postQnsDuePeriod2 = postQnsDuePeriod2.concat(dueOutsidePeriod1);

        this._postQnsDuePeriod2 = postQnsDuePeriod2;
    }

    /**
     * Calculate the post survey expired data based on question answer history.
     * Check for missed answers after the hard deadline, because anything expired
     * from period 1 simply moves into due for period 2.
     */
    public buildExpiredPostSurveys(answerMemo: HactAnswerHistorySummaryWrapper,
                                   showDaysInAdvance: number, showAllOutstanding = false) {

        this._postQnsExpiredPeriod1 = answerMemo.appropriateAnswersForOutsidePeriod1(false);

        // SURVEY 1 but in PERIOD 2
        // slipped to period 2 and expired
        let expiredAndOutsidePeriod1 = answerMemo.appropriateAnswersForOutsidePeriod1(true);

        // PERIOD 2
        // in period 2 and expired
        let expiredAndOutsidePeriod2 = answerMemo.appropriateAnswersForPeriod2(true, showDaysInAdvance, showAllOutstanding);

        // ASSIGN
        this._postQnsExpiredPeriod2 = expiredAndOutsidePeriod1.concat(expiredAndOutsidePeriod2);
    }

    public buildDueAndExpireDays(now: EccoDateTime, daysPreToPost1: number, daysPost1ToPost2: number, answersPreSurvey: IntermediateDataStructureFromHactAnswerWork[]) {

        // DAYS DUE/EXPIRE

        // get earliest in the periods
        let minpostQnsDuePeriod1 = Lazy(this._postQnsDuePeriod1).reduce((prev, curr) => {
            return prev.previousPeriodAnswer.workDate.earlierThan(curr.previousPeriodAnswer.workDate) ? prev : curr;
        });
        let minpostQnsDuePeriod2 = Lazy(this._postQnsDuePeriod2).reduce((prev, curr) =>
            prev.previousPeriodAnswer.workDate.earlierThan(curr.previousPeriodAnswer.workDate) ? prev : curr);

        // get earliest due & expire days
        if (this._postQnsDuePeriod1.length > 0) {
            let postQnsDaysDuePeriod1 = daysPreToPost1
                - now.subtractDateTime(minpostQnsDuePeriod1.previousPeriodAnswer.workDate, true).getDays();
            this._postQnsDaysDuePeriod1 = postQnsDaysDuePeriod1 > 0 ? postQnsDaysDuePeriod1 : 0;

            this._postQnsDaysExpirePeriod1 = NotificationSummaryData.daysPreToPost1Limit
                - now.subtractDateTime(minpostQnsDuePeriod1.previousPeriodAnswer.workDate, true).getDays();
        }

        if (this._postQnsDuePeriod2.length > 0) {
            let postQnsDaysDuePeriod2 = daysPost1ToPost2
                - now.subtractDateTime(minpostQnsDuePeriod2.previousPeriodAnswer.workDate, true).getDays();
            this._postQnsDaysDuePeriod2 = postQnsDaysDuePeriod2 > 0 ? postQnsDaysDuePeriod2 : 0;

            // expires is the time from the pre survey
            let preSurvey = answersPreSurvey.filter((answerPre) =>
                answerPre.questionDefId == minpostQnsDuePeriod2.previousPeriodAnswer.questionDefId)[0];
            this._postQnsDaysExpirePeriod2 = NotificationSummaryData.daysPreToEndHardLimit
                - now.subtractDateTime(preSurvey.workDate, true).getDays();
        }

    }
}
