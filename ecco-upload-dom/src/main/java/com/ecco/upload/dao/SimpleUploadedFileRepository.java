package com.ecco.upload.dao;

import com.ecco.dom.upload.SimpleUploadedFile;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface SimpleUploadedFileRepository extends UploadedFileRepository<SimpleUploadedFile> {
    @Query("from SimpleUploadedFile") // parameter is ignored
    List<SimpleUploadedFile> findFiles(final Long parentId);

    @Override
    @Query("from SimpleUploadedFile a join fetch a.uploadedBytes where a.id = ?1")
    SimpleUploadedFile findFileWithContent(final Long fileId);

    SimpleUploadedFile findOneByUploadedBytes_Id(final long uploadedBytesId);

    SimpleUploadedFile findOneById(long fileId);

    void delete(SimpleUploadedFile file);
}
